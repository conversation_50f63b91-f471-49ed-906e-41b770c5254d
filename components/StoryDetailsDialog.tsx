"use client";

import { useState } from "react";
import { Story, Milestone } from "@/lib/types";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { 
  Tabs, 
  <PERSON><PERSON><PERSON>ontent, 
  Ta<PERSON>List, 
  TabsTrigger 
} from "./ui/tabs";
import { 
  FileText, 
  CheckSquare,
  Edit,
  Tag as TagIcon
} from "lucide-react";
import ReactMarkdown from "react-markdown";

interface StoryDetailsDialogProps {
  story: Story;
  milestones: Milestone[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEdit: () => void;
}

export default function StoryDetailsDialog({
  story,
  milestones,
  open,
  onOpenChange,
  onEdit,
}: StoryDetailsDialogProps) {
  const [activeTab, setActiveTab] = useState<string>("background");

  const currentMilestone = milestones.find(m => m.id === story.milestoneId);

  // Get milestone color for styling
  const getMilestoneColor = () => {
    return currentMilestone?.color || "gray";
  };

  // Get accent color based on milestone color
  const getAccentColor = () => {
    const colorMap: Record<string, string> = {
      red: "text-red-600 dark:text-red-400",
      pink: "text-pink-600 dark:text-pink-400",
      rose: "text-rose-600 dark:text-rose-400",
      orange: "text-orange-600 dark:text-orange-400",
      amber: "text-amber-600 dark:text-amber-400",
      yellow: "text-yellow-600 dark:text-yellow-400",
      lime: "text-lime-600 dark:text-lime-400",
      green: "text-green-600 dark:text-green-400",
      emerald: "text-emerald-600 dark:text-emerald-400",
      teal: "text-teal-600 dark:text-teal-400",
      cyan: "text-cyan-600 dark:text-cyan-400",
      sky: "text-sky-600 dark:text-sky-400",
      blue: "text-blue-600 dark:text-blue-400",
      indigo: "text-indigo-600 dark:text-indigo-400",
      violet: "text-violet-600 dark:text-violet-400",
      purple: "text-purple-600 dark:text-purple-400",
      fuchsia: "text-fuchsia-600 dark:text-fuchsia-400",
      gray: "text-gray-600 dark:text-gray-400",
    };
    
    return colorMap[getMilestoneColor()] || "text-gray-600 dark:text-gray-400";
  };

  const handleEdit = () => {
    onEdit();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col bg-dialog-background">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-xl font-semibold text-foreground pr-8">
            {story.name}
          </DialogTitle>
          {currentMilestone && (
            <div className="flex items-center gap-2 mt-2">
              <span className="text-sm text-muted-foreground">Milestone:</span>
              <span className={`text-sm font-medium ${getAccentColor()}`}>
                {currentMilestone.name}
              </span>
            </div>
          )}
        </DialogHeader>
        
        <div className="flex-1 overflow-hidden">
          {/* Story metadata */}
          <div className="flex flex-wrap items-center gap-4 mb-6 pb-4 border-b border-border/30">
            {/* Tags */}
            {story.tags && story.tags.length > 0 && (
              <div className="flex items-center gap-2">
                <TagIcon className="h-4 w-4 text-muted-foreground" />
                <div className="flex flex-wrap gap-1">
                  {story.tags.map((tag, index) => (
                    <span 
                      key={index} 
                      className="text-xs px-2 py-1 rounded-full bg-secondary/50 text-secondary-foreground border border-border/20"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
            
            {/* Story Points */}
            {story.storyPoints !== undefined && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Story Points:</span>
                <div className="text-sm font-medium bg-primary/10 text-primary rounded-full h-6 w-6 flex items-center justify-center">
                  {story.storyPoints}
                </div>
              </div>
            )}
          </div>

          {/* Content tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid grid-cols-2 mb-4 bg-tabs-background">
              <TabsTrigger value="background" className="flex items-center gap-2 data-[state=active]:bg-tabs-selected">
                <FileText className="h-4 w-4" />
                Background
              </TabsTrigger>
              <TabsTrigger value="acceptance-criteria" className="flex items-center gap-2 data-[state=active]:bg-tabs-selected">
                <CheckSquare className="h-4 w-4" />
                Acceptance Criteria
              </TabsTrigger>
            </TabsList>
            
            <div className="flex-1 overflow-auto">
              <TabsContent value="background" className="mt-0 h-full">
                <div className="bg-background/50 dark:bg-background/30 rounded-lg p-4 border border-border/20 min-h-[300px]">
                  {story.background ? (
                    <div className="prose prose-sm dark:prose-invert max-w-none text-foreground dark:text-foreground">
                      <ReactMarkdown>{story.background}</ReactMarkdown>
                    </div>
                  ) : (
                    <div className="text-muted-foreground italic text-center py-8">
                      No background information provided
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="acceptance-criteria" className="mt-0 h-full">
                <div className="bg-background/50 dark:bg-background/30 rounded-lg p-4 border border-border/20 min-h-[300px]">
                  {story.acceptanceCriteria ? (
                    <div className="prose prose-sm dark:prose-invert max-w-none text-foreground dark:text-foreground">
                      <ReactMarkdown>{story.acceptanceCriteria}</ReactMarkdown>
                    </div>
                  ) : (
                    <div className="text-muted-foreground italic text-center py-8">
                      No acceptance criteria provided
                    </div>
                  )}
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>

        <DialogFooter className="flex-shrink-0 mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
          <Button onClick={handleEdit} className="flex items-center gap-2">
            <Edit className="h-4 w-4" />
            Edit Story
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
