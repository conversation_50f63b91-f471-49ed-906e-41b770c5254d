"use client";

import { useState, useEffect } from "react";
import { Story, Milestone } from "@/lib/types";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { 
  Ta<PERSON>, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from "./ui/tabs";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { 
  FileText, 
  CheckSquare,
  Tag as TagIcon,
  X 
} from "lucide-react";

interface StoryCreateDialogProps {
  milestoneId: string;
  milestones: Milestone[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreate: (milestoneId: string, story: Omit<Story, "id" | "milestoneId">) => void;
}

export default function StoryCreateDialog({
  milestoneId,
  milestones,
  open,
  onOpenChange,
  onCreate,
}: StoryCreateDialogProps) {
  const [newStory, setNewStory] = useState<Omit<Story, "id" | "milestoneId">>({
    name: "",
    background: "",
    acceptanceCriteria: "",
    tags: [],
  });
  
  const [newTag, setNewTag] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("background");
  const [selectedMilestoneId, setSelectedMilestoneId] = useState<string>(milestoneId);

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setNewStory({
        name: "",
        background: "",
        acceptanceCriteria: "",
        tags: [],
      });
      setSelectedMilestoneId(milestoneId);
      setActiveTab("background");
      setNewTag("");
    }
  }, [open, milestoneId]);

  const handleCreate = () => {
    if (newStory.name.trim() === "") return;
    onCreate(selectedMilestoneId, newStory);
    onOpenChange(false);
  };

  const handleAddTag = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && newTag.trim() !== "") {
      e.preventDefault();
      if (!newStory.tags?.includes(newTag.trim())) {
        setNewStory({
          ...newStory,
          tags: [...(newStory.tags || []), newTag.trim()],
        });
      }
      setNewTag("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setNewStory({
      ...newStory,
      tags: newStory.tags?.filter(tag => tag !== tagToRemove) || [],
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Story</DialogTitle>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          {/* Title */}
          <div className="grid gap-2">
            <Label htmlFor="story-title">Title</Label>
            <Input
              id="story-title"
              value={newStory.name}
              onChange={(e) => setNewStory({ ...newStory, name: e.target.value })}
              placeholder="Enter story title"
              autoFocus
            />
          </div>

          {/* Tabs for Background and Acceptance Criteria */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-2 bg-tabs-background">
              <TabsTrigger value="background" className="flex items-center gap-2 data-[state=active]:bg-tabs-selected">
                <FileText className="h-4 w-4" />
                Background
              </TabsTrigger>
              <TabsTrigger value="acceptance-criteria" className="flex items-center gap-2 data-[state=active]:bg-tabs-selected">
                <CheckSquare className="h-4 w-4" />
                Acceptance Criteria
              </TabsTrigger>
            </TabsList>

            <TabsContent value="background" className="mt-4">
              <div className="grid gap-2">
                <Label htmlFor="story-background" className="text-foreground font-medium">Background (Markdown supported)</Label>
                <Textarea
                  id="story-background"
                  value={newStory.background}
                  onChange={(e) => setNewStory({ ...newStory, background: e.target.value })}
                  className="min-h-[150px] border-border/30 text-foreground placeholder:text-muted-foreground/70"
                  placeholder="Enter background information for this story..."
                />
              </div>
            </TabsContent>

            <TabsContent value="acceptance-criteria" className="mt-4">
              <div className="grid gap-2">
                <Label htmlFor="story-acceptance-criteria" className="text-foreground font-medium">Acceptance Criteria (Markdown supported)</Label>
                <Textarea
                  id="story-acceptance-criteria"
                  value={newStory.acceptanceCriteria}
                  onChange={(e) => setNewStory({ ...newStory, acceptanceCriteria: e.target.value })}
                  className="min-h-[150px] border-border/30 text-foreground placeholder:text-muted-foreground/70"
                  placeholder="Enter acceptance criteria for this story..."
                />
              </div>
            </TabsContent>
          </Tabs>

          {/* Milestone Dropdown */}
          <div className="grid gap-2">
            <Label htmlFor="story-milestone">Milestone</Label>
            <Select 
              value={selectedMilestoneId} 
              onValueChange={(value: string) => setSelectedMilestoneId(value)}
            >
              <SelectTrigger id="story-milestone">
                <SelectValue placeholder="Select a milestone" />
              </SelectTrigger>
              <SelectContent>
                {milestones.map((milestone) => (
                  <SelectItem key={milestone.id} value={milestone.id}>
                    {milestone.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Story Points */}
          <div className="grid gap-2">
            <Label htmlFor="story-points">Story Points</Label>
            <Select 
              value={newStory.storyPoints?.toString() || "none"} 
              onValueChange={(value: string) => setNewStory({ 
                ...newStory, 
                storyPoints: value === "none" ? undefined : parseInt(value, 10) 
              })}
            >
              <SelectTrigger id="story-points">
                <SelectValue placeholder="Assign story points" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">Not set</SelectItem>
                <SelectItem value="1">1</SelectItem>
                <SelectItem value="2">2</SelectItem>
                <SelectItem value="3">3</SelectItem>
                <SelectItem value="5">5</SelectItem>
                <SelectItem value="8">8</SelectItem>
                <SelectItem value="13">13</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Tags */}
          <div className="grid gap-2">
            <Label className="flex items-center gap-2" htmlFor="story-tags">
              <TagIcon className="h-4 w-4" />
              Tags
            </Label>
            <Input
              id="story-tags"
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              onKeyDown={handleAddTag}
              placeholder="Add tag and press Enter..."
            />
            
            {/* Display tags */}
            <div className="flex flex-wrap gap-2 mt-2">
              {newStory.tags?.map((tag) => (
                <div 
                  key={tag} 
                  className="flex items-center gap-1 bg-secondary text-secondary-foreground px-2 py-1 rounded-md text-sm"
                >
                  {tag}
                  <button 
                    onClick={() => handleRemoveTag(tag)}
                    className="text-secondary-foreground/70 hover:text-secondary-foreground"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleCreate} disabled={newStory.name.trim() === ""}>
            Create
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
